"""
Framework Bridge - Clean interface to the new Language-Agnostic Framework
Provides a simple interface to the new framework system.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import asyncio

# Import new framework components
from framework_integration import IntegratedCodeAnalysisSystem
from language_registry import create_language_registry

logger = logging.getLogger(__name__)

class FrameworkBridge:
    """
    Bridge to the new Language-Agnostic Framework.
    Provides clean interface to the new framework system.
    """
    
    def __init__(self, repo_path: str, use_new_framework: bool = True):
        self.repo_path = str(repo_path)  # Ensure it's a string
        
        # Initialize new system
        self.new_system = IntegratedCodeAnalysisSystem()
        
        logger.info(f"FrameworkBridge initialized for {repo_path}")
        logger.info(f"Using new language framework with 45+ languages")
    
    def process_repository(self, exclude_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Process repository using the new language framework.
        
        Args:
            exclude_dirs: Directories to exclude from processing
            
        Returns:
            List of processed chunks compatible with existing vector DB
        """
        
        # Run async method synchronously
        return asyncio.run(self._process_with_new_framework(exclude_dirs))

    def process_codebase(self, source_path: str, exclude_dirs: Optional[set] = None) -> List[Dict[str, Any]]:
        """
        Process codebase using the new language framework
        
        Args:
            source_path: Path to the source code directory
            exclude_dirs: Set of directories to exclude from processing
            
        Returns:
            List of processed chunks compatible with existing vector DB
        """
        # Update the repo path
        self.repo_path = str(source_path)
        
        # Convert set to list for compatibility
        exclude_list = list(exclude_dirs) if exclude_dirs else None
        
        return self.process_repository(exclude_list)

    async def _process_with_new_framework(self, exclude_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Process using new Language-Agnostic Framework"""
        logger.info("Processing with new Language-Agnostic Framework")
        
        # Analyze codebase with new system
        result = await self.new_system.analyze_codebase(self.repo_path)
        
        if not result["success"]:
            raise Exception(f"Framework processing failed: {result.get('error', 'Unknown error')}")
        
        # Convert new framework results to old format for compatibility
        chunks = self._convert_new_to_old_format(result)
        
        logger.info(f"Processed {len(chunks)} chunks using new framework")
        return chunks
    
    def _convert_new_to_old_format(self, new_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert new framework results to old format for backward compatibility"""
        
        chunks = []
        
        # Extract chunk generation results
        pipeline_results = new_result.get("pipeline_results", {})
        chunk_generation = pipeline_results.get("chunk_generation")
        
        if chunk_generation and chunk_generation.get("status") == "completed":
            generated_chunks = chunk_generation.get("result", {}).get("chunks", [])
            
            for chunk in generated_chunks:
                # Convert new chunk format to old format
                old_chunk = {
                    "content": chunk.content,
                    "metadata": {
                        "file_path": chunk.metadata.get("file_path", ""),
                        "chunk_type": chunk.chunk_type,
                        "language": chunk.metadata.get("language", "unknown"),
                        "start_line": chunk.metadata.get("start_line", 0),
                        "end_line": chunk.metadata.get("end_line", 0),
                        "semantic_tags": chunk.metadata.get("semantic_tags", []),
                        "complexity_score": chunk.metadata.get("complexity_score", 0),
                        "quality_score": chunk.metadata.get("quality_score", 0)
                    }
                }
                chunks.append(old_chunk)
        
        # If no chunks from new system, create basic chunks from file analysis
        if not chunks:
            file_analysis = new_result.get("file_relationships", {})
            for file_path, analysis in file_analysis.items():
                chunk = {
                    "content": f"# File: {file_path}\n# Analysis: {analysis}",
                    "metadata": {
                        "file_path": file_path,
                        "chunk_type": "file_analysis",
                        "language": analysis.get("language", "unknown"),
                        "semantic_tags": [],
                        "complexity_score": 1,
                        "quality_score": 1
                    }
                }
                chunks.append(chunk)
        
        return chunks
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages from new framework"""
        if self.new_system:
            return list(self.new_system.framework.get_supported_languages())
        return []
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about the new framework system"""
        return {
            "framework_mode": True,
            "repo_path": self.repo_path,
            "system": {
                "available": self.new_system is not None,
                "class": "IntegratedCodeAnalysisSystem",
                "supported_languages": len(self.new_system.framework.get_supported_languages())
            }
        }
